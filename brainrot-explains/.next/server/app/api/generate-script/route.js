"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-script/route";
exports.ids = ["app/api/generate-script/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-script%2Froute&page=%2Fapi%2Fgenerate-script%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-script%2Froute.ts&appDir=%2FUsers%2Fanjanbharadwaj%2FDocuments%2F2025%2Ftiktok-videogen%2Fbrainrot-explains%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fanjanbharadwaj%2FDocuments%2F2025%2Ftiktok-videogen%2Fbrainrot-explains&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-script%2Froute&page=%2Fapi%2Fgenerate-script%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-script%2Froute.ts&appDir=%2FUsers%2Fanjanbharadwaj%2FDocuments%2F2025%2Ftiktok-videogen%2Fbrainrot-explains%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fanjanbharadwaj%2FDocuments%2F2025%2Ftiktok-videogen%2Fbrainrot-explains&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/node-polyfill-headers */ \"(rsc)/./node_modules/next/dist/server/node-polyfill-headers.js\");\n/* harmony import */ var next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var _Users_anjanbharadwaj_Documents_2025_tiktok_videogen_brainrot_explains_src_app_api_generate_script_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/generate-script/route.ts */ \"(rsc)/./src/app/api/generate-script/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_2__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-script/route\",\n        pathname: \"/api/generate-script\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-script/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/2025/tiktok-videogen/brainrot-explains/src/app/api/generate-script/route.ts\",\n    nextConfigOutput,\n    userland: _Users_anjanbharadwaj_Documents_2025_tiktok_videogen_brainrot_explains_src_app_api_generate_script_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/generate-script/route\";\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-script%2Froute&page=%2Fapi%2Fgenerate-script%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-script%2Froute.ts&appDir=%2FUsers%2Fanjanbharadwaj%2FDocuments%2F2025%2Ftiktok-videogen%2Fbrainrot-explains%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fanjanbharadwaj%2FDocuments%2F2025%2Ftiktok-videogen%2Fbrainrot-explains&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/generate-script/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/generate-script/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n\nasync function POST(request) {\n    try {\n        const { topic } = await request.json();\n        if (!topic) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Topic is required\"\n            }, {\n                status: 400\n            });\n        }\n        // For now, we'll use a mock script generator\n        // In a real implementation, this would call OpenAI GPT-4\n        const script = generateMockScript(topic);\n        // Simulate API delay\n        await new Promise((resolve)=>setTimeout(resolve, 2000));\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            script\n        });\n    } catch (error) {\n        console.error(\"Error generating script:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to generate script\"\n        }, {\n            status: 500\n        });\n    }\n}\nfunction generateMockScript(topic) {\n    const scripts = {\n        \"Google File System\": `Stewie: \"Peter, what's this Google File System thing? Sounds like some sort of nerd hard drive.\"\n\nPeter: \"Heh heh, nah Stewie, it's like Google's way of storing a bazillion files across a ton of machines without things breaking. Big files, split across servers like pizza slices.\"\n\nStewie: \"Why not just use one giant server?\"\n\nPeter: \"Because then when it crashes, boom—game over. This way, if one server eats it, others keep the data safe. It's fault-tolerant, like Meg's ego.\"\n\nStewie: \"So it's basically digital redundancy?\"\n\nPeter: \"Exactly! Plus it handles massive amounts of data, like my browser history. Google needed something that could scale bigger than my appetite at a buffet.\"`,\n        \"Attention is All You Need\": `Stewie: \"Peter, explain this 'Attention is All You Need' paper. It sounds like relationship advice.\"\n\nPeter: \"Hah! Nah Stewie, it's about transformers—not the robots, the AI kind. Instead of reading text word by word like I read beer labels, it looks at ALL words at once.\"\n\nStewie: \"How does that work exactly?\"\n\nPeter: \"It's like being at a party and listening to every conversation simultaneously, but only paying attention to the important parts. The 'attention mechanism' decides what's worth focusing on.\"\n\nStewie: \"And this revolutionized AI?\"\n\nPeter: \"Oh yeah! Before this, AI was like reading a book with a flashlight—one word at a time. Now it's like having stadium lights on the whole page. Way faster and smarter, like me after my third beer.\"`,\n        \"MapReduce\": `Stewie: \"Peter, what's MapReduce? Sounds like a diet plan.\"\n\nPeter: \"Heh, close! It's Google's way of processing huge amounts of data by breaking it into smaller chunks. Like eating a whole pizza—you don't shove it all in your mouth at once.\"\n\nStewie: \"So it divides the work?\"\n\nPeter: \"Exactly! The 'Map' part spreads the work across many computers, like having multiple people each eat a slice. Then 'Reduce' combines all the results back together.\"\n\nStewie: \"Why is this useful?\"\n\nPeter: \"Because some jobs are too big for one computer, like counting all the cat videos on the internet. You need an army of computers working together, like my fantasy football league but actually productive.\"`,\n        \"Bitcoin Blockchain\": `Stewie: \"Peter, explain Bitcoin's blockchain. Is it like a chain made of blocks?\"\n\nPeter: \"Hah! Not quite, Stewie. It's like a ledger book that everyone has a copy of, but nobody can cheat because everyone's watching everyone else.\"\n\nStewie: \"How does that prevent fraud?\"\n\nPeter: \"Well, if I try to spend the same Bitcoin twice, it's like trying to use the same dollar bill at two different stores simultaneously. The network catches you faster than Lois catches me hiding beer.\"\n\nStewie: \"And the mining?\"\n\nPeter: \"That's just computers solving really hard math puzzles to add new pages to the ledger. Winners get Bitcoin, like a really nerdy lottery where the prize is digital money.\"`,\n        default: `Stewie: \"Peter, can you explain ${topic}? I'm completely lost.\"\n\nPeter: \"Oh boy, ${topic}? That's a tough one, Stewie. It's like... uh... imagine if technology and science had a baby, and that baby was really complicated.\"\n\nStewie: \"That's not very helpful, Peter.\"\n\nPeter: \"Look, the important thing about ${topic} is that smart people figured out how to make computers do smart things. It's probably got something to do with algorithms, data, and making life easier.\"\n\nStewie: \"I suppose that's... somewhat accurate?\"\n\nPeter: \"Hey, I may not understand the details, but I know it's important enough that people write papers about it. That's gotta count for something, right?\"`\n    };\n    return scripts[topic] || scripts.default;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9nZW5lcmF0ZS1zY3JpcHQvcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBd0Q7QUFFakQsZUFBZUMsS0FBS0MsT0FBb0I7SUFDN0MsSUFBSTtRQUNGLE1BQU0sRUFBRUMsS0FBSyxFQUFFLEdBQUcsTUFBTUQsUUFBUUUsSUFBSTtRQUVwQyxJQUFJLENBQUNELE9BQU87WUFDVixPQUFPSCxrRkFBWUEsQ0FBQ0ksSUFBSSxDQUN0QjtnQkFBRUMsT0FBTztZQUFvQixHQUM3QjtnQkFBRUMsUUFBUTtZQUFJO1FBRWxCO1FBRUEsNkNBQTZDO1FBQzdDLHlEQUF5RDtRQUN6RCxNQUFNQyxTQUFTQyxtQkFBbUJMO1FBRWxDLHFCQUFxQjtRQUNyQixNQUFNLElBQUlNLFFBQVFDLENBQUFBLFVBQVdDLFdBQVdELFNBQVM7UUFFakQsT0FBT1Ysa0ZBQVlBLENBQUNJLElBQUksQ0FBQztZQUFFRztRQUFPO0lBQ3BDLEVBQUUsT0FBT0YsT0FBTztRQUNkTyxRQUFRUCxLQUFLLENBQUMsNEJBQTRCQTtRQUMxQyxPQUFPTCxrRkFBWUEsQ0FBQ0ksSUFBSSxDQUN0QjtZQUFFQyxPQUFPO1FBQTRCLEdBQ3JDO1lBQUVDLFFBQVE7UUFBSTtJQUVsQjtBQUNGO0FBRUEsU0FBU0UsbUJBQW1CTCxLQUFhO0lBQ3ZDLE1BQU1VLFVBQVU7UUFDZCxzQkFBc0IsQ0FBQzs7Ozs7Ozs7OztpS0FVc0ksQ0FBQztRQUU5Siw2QkFBNkIsQ0FBQzs7Ozs7Ozs7OzsyTUFVeUssQ0FBQztRQUV4TSxhQUFhLENBQUM7Ozs7Ozs7Ozs7bU5BVWlNLENBQUM7UUFFaE4sc0JBQXNCLENBQUM7Ozs7Ozs7Ozs7a0xBVXVKLENBQUM7UUFFL0tDLFNBQVMsQ0FBQyxnQ0FBZ0MsRUFBRVgsTUFBTTs7Z0JBRXRDLEVBQUVBLE1BQU07Ozs7d0NBSWdCLEVBQUVBLE1BQU07Ozs7NEpBSTRHLENBQUM7SUFDM0o7SUFFQSxPQUFPVSxPQUFPLENBQUNWLE1BQThCLElBQUlVLFFBQVFDLE9BQU87QUFDbEUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9icmFpbnJvdC1leHBsYWlucy8uL3NyYy9hcHAvYXBpL2dlbmVyYXRlLXNjcmlwdC9yb3V0ZS50cz83YmNmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE5leHRSZXF1ZXN0LCBOZXh0UmVzcG9uc2UgfSBmcm9tICduZXh0L3NlcnZlcic7XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBQT1NUKHJlcXVlc3Q6IE5leHRSZXF1ZXN0KSB7XG4gIHRyeSB7XG4gICAgY29uc3QgeyB0b3BpYyB9ID0gYXdhaXQgcmVxdWVzdC5qc29uKCk7XG5cbiAgICBpZiAoIXRvcGljKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHsgZXJyb3I6ICdUb3BpYyBpcyByZXF1aXJlZCcgfSxcbiAgICAgICAgeyBzdGF0dXM6IDQwMCB9XG4gICAgICApO1xuICAgIH1cblxuICAgIC8vIEZvciBub3csIHdlJ2xsIHVzZSBhIG1vY2sgc2NyaXB0IGdlbmVyYXRvclxuICAgIC8vIEluIGEgcmVhbCBpbXBsZW1lbnRhdGlvbiwgdGhpcyB3b3VsZCBjYWxsIE9wZW5BSSBHUFQtNFxuICAgIGNvbnN0IHNjcmlwdCA9IGdlbmVyYXRlTW9ja1NjcmlwdCh0b3BpYyk7XG5cbiAgICAvLyBTaW11bGF0ZSBBUEkgZGVsYXlcbiAgICBhd2FpdCBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgMjAwMCkpO1xuXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgc2NyaXB0IH0pO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGdlbmVyYXRpbmcgc2NyaXB0OicsIGVycm9yKTtcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICB7IGVycm9yOiAnRmFpbGVkIHRvIGdlbmVyYXRlIHNjcmlwdCcgfSxcbiAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgICk7XG4gIH1cbn1cblxuZnVuY3Rpb24gZ2VuZXJhdGVNb2NrU2NyaXB0KHRvcGljOiBzdHJpbmcpOiBzdHJpbmcge1xuICBjb25zdCBzY3JpcHRzID0ge1xuICAgICdHb29nbGUgRmlsZSBTeXN0ZW0nOiBgU3Rld2llOiBcIlBldGVyLCB3aGF0J3MgdGhpcyBHb29nbGUgRmlsZSBTeXN0ZW0gdGhpbmc/IFNvdW5kcyBsaWtlIHNvbWUgc29ydCBvZiBuZXJkIGhhcmQgZHJpdmUuXCJcblxuUGV0ZXI6IFwiSGVoIGhlaCwgbmFoIFN0ZXdpZSwgaXQncyBsaWtlIEdvb2dsZSdzIHdheSBvZiBzdG9yaW5nIGEgYmF6aWxsaW9uIGZpbGVzIGFjcm9zcyBhIHRvbiBvZiBtYWNoaW5lcyB3aXRob3V0IHRoaW5ncyBicmVha2luZy4gQmlnIGZpbGVzLCBzcGxpdCBhY3Jvc3Mgc2VydmVycyBsaWtlIHBpenphIHNsaWNlcy5cIlxuXG5TdGV3aWU6IFwiV2h5IG5vdCBqdXN0IHVzZSBvbmUgZ2lhbnQgc2VydmVyP1wiXG5cblBldGVyOiBcIkJlY2F1c2UgdGhlbiB3aGVuIGl0IGNyYXNoZXMsIGJvb23igJRnYW1lIG92ZXIuIFRoaXMgd2F5LCBpZiBvbmUgc2VydmVyIGVhdHMgaXQsIG90aGVycyBrZWVwIHRoZSBkYXRhIHNhZmUuIEl0J3MgZmF1bHQtdG9sZXJhbnQsIGxpa2UgTWVnJ3MgZWdvLlwiXG5cblN0ZXdpZTogXCJTbyBpdCdzIGJhc2ljYWxseSBkaWdpdGFsIHJlZHVuZGFuY3k/XCJcblxuUGV0ZXI6IFwiRXhhY3RseSEgUGx1cyBpdCBoYW5kbGVzIG1hc3NpdmUgYW1vdW50cyBvZiBkYXRhLCBsaWtlIG15IGJyb3dzZXIgaGlzdG9yeS4gR29vZ2xlIG5lZWRlZCBzb21ldGhpbmcgdGhhdCBjb3VsZCBzY2FsZSBiaWdnZXIgdGhhbiBteSBhcHBldGl0ZSBhdCBhIGJ1ZmZldC5cImAsXG5cbiAgICAnQXR0ZW50aW9uIGlzIEFsbCBZb3UgTmVlZCc6IGBTdGV3aWU6IFwiUGV0ZXIsIGV4cGxhaW4gdGhpcyAnQXR0ZW50aW9uIGlzIEFsbCBZb3UgTmVlZCcgcGFwZXIuIEl0IHNvdW5kcyBsaWtlIHJlbGF0aW9uc2hpcCBhZHZpY2UuXCJcblxuUGV0ZXI6IFwiSGFoISBOYWggU3Rld2llLCBpdCdzIGFib3V0IHRyYW5zZm9ybWVyc+KAlG5vdCB0aGUgcm9ib3RzLCB0aGUgQUkga2luZC4gSW5zdGVhZCBvZiByZWFkaW5nIHRleHQgd29yZCBieSB3b3JkIGxpa2UgSSByZWFkIGJlZXIgbGFiZWxzLCBpdCBsb29rcyBhdCBBTEwgd29yZHMgYXQgb25jZS5cIlxuXG5TdGV3aWU6IFwiSG93IGRvZXMgdGhhdCB3b3JrIGV4YWN0bHk/XCJcblxuUGV0ZXI6IFwiSXQncyBsaWtlIGJlaW5nIGF0IGEgcGFydHkgYW5kIGxpc3RlbmluZyB0byBldmVyeSBjb252ZXJzYXRpb24gc2ltdWx0YW5lb3VzbHksIGJ1dCBvbmx5IHBheWluZyBhdHRlbnRpb24gdG8gdGhlIGltcG9ydGFudCBwYXJ0cy4gVGhlICdhdHRlbnRpb24gbWVjaGFuaXNtJyBkZWNpZGVzIHdoYXQncyB3b3J0aCBmb2N1c2luZyBvbi5cIlxuXG5TdGV3aWU6IFwiQW5kIHRoaXMgcmV2b2x1dGlvbml6ZWQgQUk/XCJcblxuUGV0ZXI6IFwiT2ggeWVhaCEgQmVmb3JlIHRoaXMsIEFJIHdhcyBsaWtlIHJlYWRpbmcgYSBib29rIHdpdGggYSBmbGFzaGxpZ2h04oCUb25lIHdvcmQgYXQgYSB0aW1lLiBOb3cgaXQncyBsaWtlIGhhdmluZyBzdGFkaXVtIGxpZ2h0cyBvbiB0aGUgd2hvbGUgcGFnZS4gV2F5IGZhc3RlciBhbmQgc21hcnRlciwgbGlrZSBtZSBhZnRlciBteSB0aGlyZCBiZWVyLlwiYCxcblxuICAgICdNYXBSZWR1Y2UnOiBgU3Rld2llOiBcIlBldGVyLCB3aGF0J3MgTWFwUmVkdWNlPyBTb3VuZHMgbGlrZSBhIGRpZXQgcGxhbi5cIlxuXG5QZXRlcjogXCJIZWgsIGNsb3NlISBJdCdzIEdvb2dsZSdzIHdheSBvZiBwcm9jZXNzaW5nIGh1Z2UgYW1vdW50cyBvZiBkYXRhIGJ5IGJyZWFraW5nIGl0IGludG8gc21hbGxlciBjaHVua3MuIExpa2UgZWF0aW5nIGEgd2hvbGUgcGl6emHigJR5b3UgZG9uJ3Qgc2hvdmUgaXQgYWxsIGluIHlvdXIgbW91dGggYXQgb25jZS5cIlxuXG5TdGV3aWU6IFwiU28gaXQgZGl2aWRlcyB0aGUgd29yaz9cIlxuXG5QZXRlcjogXCJFeGFjdGx5ISBUaGUgJ01hcCcgcGFydCBzcHJlYWRzIHRoZSB3b3JrIGFjcm9zcyBtYW55IGNvbXB1dGVycywgbGlrZSBoYXZpbmcgbXVsdGlwbGUgcGVvcGxlIGVhY2ggZWF0IGEgc2xpY2UuIFRoZW4gJ1JlZHVjZScgY29tYmluZXMgYWxsIHRoZSByZXN1bHRzIGJhY2sgdG9nZXRoZXIuXCJcblxuU3Rld2llOiBcIldoeSBpcyB0aGlzIHVzZWZ1bD9cIlxuXG5QZXRlcjogXCJCZWNhdXNlIHNvbWUgam9icyBhcmUgdG9vIGJpZyBmb3Igb25lIGNvbXB1dGVyLCBsaWtlIGNvdW50aW5nIGFsbCB0aGUgY2F0IHZpZGVvcyBvbiB0aGUgaW50ZXJuZXQuIFlvdSBuZWVkIGFuIGFybXkgb2YgY29tcHV0ZXJzIHdvcmtpbmcgdG9nZXRoZXIsIGxpa2UgbXkgZmFudGFzeSBmb290YmFsbCBsZWFndWUgYnV0IGFjdHVhbGx5IHByb2R1Y3RpdmUuXCJgLFxuXG4gICAgJ0JpdGNvaW4gQmxvY2tjaGFpbic6IGBTdGV3aWU6IFwiUGV0ZXIsIGV4cGxhaW4gQml0Y29pbidzIGJsb2NrY2hhaW4uIElzIGl0IGxpa2UgYSBjaGFpbiBtYWRlIG9mIGJsb2Nrcz9cIlxuXG5QZXRlcjogXCJIYWghIE5vdCBxdWl0ZSwgU3Rld2llLiBJdCdzIGxpa2UgYSBsZWRnZXIgYm9vayB0aGF0IGV2ZXJ5b25lIGhhcyBhIGNvcHkgb2YsIGJ1dCBub2JvZHkgY2FuIGNoZWF0IGJlY2F1c2UgZXZlcnlvbmUncyB3YXRjaGluZyBldmVyeW9uZSBlbHNlLlwiXG5cblN0ZXdpZTogXCJIb3cgZG9lcyB0aGF0IHByZXZlbnQgZnJhdWQ/XCJcblxuUGV0ZXI6IFwiV2VsbCwgaWYgSSB0cnkgdG8gc3BlbmQgdGhlIHNhbWUgQml0Y29pbiB0d2ljZSwgaXQncyBsaWtlIHRyeWluZyB0byB1c2UgdGhlIHNhbWUgZG9sbGFyIGJpbGwgYXQgdHdvIGRpZmZlcmVudCBzdG9yZXMgc2ltdWx0YW5lb3VzbHkuIFRoZSBuZXR3b3JrIGNhdGNoZXMgeW91IGZhc3RlciB0aGFuIExvaXMgY2F0Y2hlcyBtZSBoaWRpbmcgYmVlci5cIlxuXG5TdGV3aWU6IFwiQW5kIHRoZSBtaW5pbmc/XCJcblxuUGV0ZXI6IFwiVGhhdCdzIGp1c3QgY29tcHV0ZXJzIHNvbHZpbmcgcmVhbGx5IGhhcmQgbWF0aCBwdXp6bGVzIHRvIGFkZCBuZXcgcGFnZXMgdG8gdGhlIGxlZGdlci4gV2lubmVycyBnZXQgQml0Y29pbiwgbGlrZSBhIHJlYWxseSBuZXJkeSBsb3R0ZXJ5IHdoZXJlIHRoZSBwcml6ZSBpcyBkaWdpdGFsIG1vbmV5LlwiYCxcblxuICAgIGRlZmF1bHQ6IGBTdGV3aWU6IFwiUGV0ZXIsIGNhbiB5b3UgZXhwbGFpbiAke3RvcGljfT8gSSdtIGNvbXBsZXRlbHkgbG9zdC5cIlxuXG5QZXRlcjogXCJPaCBib3ksICR7dG9waWN9PyBUaGF0J3MgYSB0b3VnaCBvbmUsIFN0ZXdpZS4gSXQncyBsaWtlLi4uIHVoLi4uIGltYWdpbmUgaWYgdGVjaG5vbG9neSBhbmQgc2NpZW5jZSBoYWQgYSBiYWJ5LCBhbmQgdGhhdCBiYWJ5IHdhcyByZWFsbHkgY29tcGxpY2F0ZWQuXCJcblxuU3Rld2llOiBcIlRoYXQncyBub3QgdmVyeSBoZWxwZnVsLCBQZXRlci5cIlxuXG5QZXRlcjogXCJMb29rLCB0aGUgaW1wb3J0YW50IHRoaW5nIGFib3V0ICR7dG9waWN9IGlzIHRoYXQgc21hcnQgcGVvcGxlIGZpZ3VyZWQgb3V0IGhvdyB0byBtYWtlIGNvbXB1dGVycyBkbyBzbWFydCB0aGluZ3MuIEl0J3MgcHJvYmFibHkgZ290IHNvbWV0aGluZyB0byBkbyB3aXRoIGFsZ29yaXRobXMsIGRhdGEsIGFuZCBtYWtpbmcgbGlmZSBlYXNpZXIuXCJcblxuU3Rld2llOiBcIkkgc3VwcG9zZSB0aGF0J3MuLi4gc29tZXdoYXQgYWNjdXJhdGU/XCJcblxuUGV0ZXI6IFwiSGV5LCBJIG1heSBub3QgdW5kZXJzdGFuZCB0aGUgZGV0YWlscywgYnV0IEkga25vdyBpdCdzIGltcG9ydGFudCBlbm91Z2ggdGhhdCBwZW9wbGUgd3JpdGUgcGFwZXJzIGFib3V0IGl0LiBUaGF0J3MgZ290dGEgY291bnQgZm9yIHNvbWV0aGluZywgcmlnaHQ/XCJgXG4gIH07XG5cbiAgcmV0dXJuIHNjcmlwdHNbdG9waWMgYXMga2V5b2YgdHlwZW9mIHNjcmlwdHNdIHx8IHNjcmlwdHMuZGVmYXVsdDtcbn1cbiJdLCJuYW1lcyI6WyJOZXh0UmVzcG9uc2UiLCJQT1NUIiwicmVxdWVzdCIsInRvcGljIiwianNvbiIsImVycm9yIiwic3RhdHVzIiwic2NyaXB0IiwiZ2VuZXJhdGVNb2NrU2NyaXB0IiwiUHJvbWlzZSIsInJlc29sdmUiLCJzZXRUaW1lb3V0IiwiY29uc29sZSIsInNjcmlwdHMiLCJkZWZhdWx0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/generate-script/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-script%2Froute&page=%2Fapi%2Fgenerate-script%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-script%2Froute.ts&appDir=%2FUsers%2Fanjanbharadwaj%2FDocuments%2F2025%2Ftiktok-videogen%2Fbrainrot-explains%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fanjanbharadwaj%2FDocuments%2F2025%2Ftiktok-videogen%2Fbrainrot-explains&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();