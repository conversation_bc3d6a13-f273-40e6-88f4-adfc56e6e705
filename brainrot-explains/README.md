# 🧠 <PERSON><PERSON> Explains

Turn complex technical topics into hilarious 30-60 second videos with AI-generated <PERSON><PERSON><PERSON> & <PERSON> dialogue over engaging gameplay backgrounds.

## ✨ Features

- **AI Script Generation**: Creates entertaining dialogue between <PERSON><PERSON><PERSON> and <PERSON> explaining technical concepts
- **Voice Synthesis**: Converts scripts to character-specific AI voices (integration ready)
- **Gameplay Backgrounds**: Overlays dialogue on engaging gameplay footage
- **Social Media Ready**: Optimized for TikTok, Instagram Reels, and YouTube Shorts
- **Download & Share**: Easy export and sharing functionality

## 🚀 Quick Start

### Prerequisites

- Node.js 18.10.0 or higher
- npm or yarn package manager

### Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd brainrot-explains
```

2. Install dependencies:

```bash
npm install
```

3. Set up environment variables:

```bash
cp .env.example .env.local
```

4. Configure your API keys in `.env.local`:

   - OpenAI API key for script generation
   - Text-to-Speech API keys (Eleven<PERSON><PERSON>s, Uberduck, etc.)
   - Voice IDs for <PERSON><PERSON><PERSON> and <PERSON>

5. Start the development server:

```bash
npm run dev
```

6. Open [http://localhost:3000](http://localhost:3000) in your browser

## 🛠️ Tech Stack

- **Frontend**: Next.js 13, React 18, Tailwind CSS
- **Backend**: Next.js API Routes
- **AI Integration**: OpenAI GPT-4 (for script generation)
- **Voice Synthesis**: ElevenLabs/Uberduck APIs
- **Video Processing**: FFmpeg, Remotion
- **Deployment**: Vercel (recommended)

## 📁 Project Structure

```
brainrot-explains/
├── src/
│   ├── app/
│   │   ├── api/
│   │   │   └── generate-script/     # Script generation API
│   │   ├── generate/                # Video generation page
│   │   ├── globals.css              # Global styles
│   │   ├── layout.tsx               # Root layout
│   │   └── page.tsx                 # Home page
│   └── components/                  # Reusable components (future)
├── public/                          # Static assets
├── .env.example                     # Environment variables template
└── README.md
```

## 🎯 Usage

1. **Enter a Topic**: Type any technical concept (e.g., "Google File System", "Attention is All You Need")
2. **Generate Script**: AI creates a humorous dialogue between Stewie and Peter
3. **Process Video**: System generates voices and combines with gameplay footage
4. **Download & Share**: Export your video for social media

## 🔧 Configuration

### API Keys Setup

1. **OpenAI API**: Get your API key from [OpenAI Platform](https://platform.openai.com/)
2. **Text-to-Speech**: Choose from:
   - [ElevenLabs](https://elevenlabs.io/) - High-quality voice cloning
   - [Uberduck](https://uberduck.ai/) - Character voice synthesis
3. **Voice IDs**: Configure character-specific voice IDs in your environment

### Example Topics

- Google File System
- Attention is All You Need
- MapReduce
- Bitcoin Blockchain
- React Virtual DOM
- Kubernetes Architecture
- GraphQL vs REST
- Machine Learning Basics

## 🚧 Development Status

- ✅ Home page with topic input
- ✅ Script generation API (mock implementation)
- ✅ Loading page with progress tracking
- ✅ Video output page with player
- 🔄 OpenAI GPT-4 integration (ready for API key)
- 🔄 Text-to-Speech integration (ready for API key)
- 🔄 Video processing with FFmpeg
- 🔄 Gameplay background integration

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Family Guy characters (Stewie & Peter Griffin) for inspiration
- OpenAI for GPT-4 technology
- Next.js team for the amazing framework
- Tailwind CSS for beautiful styling
