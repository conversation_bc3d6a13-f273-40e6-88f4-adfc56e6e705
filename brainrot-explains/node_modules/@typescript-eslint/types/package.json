{"name": "@typescript-eslint/types", "version": "6.21.0", "description": "Types for the TypeScript-ESTree AST spec", "files": ["dist", "_ts4.3", "package.json", "README.md", "LICENSE"], "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "engines": {"node": "^16.0.0 || >=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/types"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"copy-ast-spec": "tsx ./tools/copy-ast-spec.ts", "build": "tsc -b tsconfig.build.json", "postbuild": "downlevel-dts dist _ts4.3/dist --to=4.3", "clean": "tsc -b tsconfig.build.json --clean", "postclean": "rimraf dist && rimraf src/generated && rimraf _ts3.4 && rimraf _ts4.3 && rimraf coverage", "format": "prettier --write \"./**/*.{ts,mts,cts,tsx,js,mjs,cjs,jsx,json,md,css}\" --ignore-path ../../.prettierignore", "generate-lib": "npx nx run scope-manager:generate-lib", "lint": "npx nx lint", "typecheck": "tsc -p tsconfig.json --noEmit"}, "nx": {"targets": {"copy-ast-spec": {"dependsOn": ["^build"], "outputs": ["{projectRoot}/src/generated"], "cache": true}, "build": {"dependsOn": ["^build", "copy-ast-spec"]}}}, "devDependencies": {"downlevel-dts": "*", "prettier": "^3.0.3", "rimraf": "*", "tsx": "*", "typescript": "*"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "typesVersions": {"<4.7": {"*": ["_ts4.3/*"]}}}