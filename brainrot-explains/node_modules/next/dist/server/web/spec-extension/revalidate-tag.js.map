{"version": 3, "sources": ["../../../../src/server/web/spec-extension/revalidate-tag.ts"], "names": ["revalidateTag", "tag", "staticGenerationAsyncStorage", "fetch", "__nextGetStaticStore", "store", "getStore", "incrementalCache", "Error", "revalidatedTags", "includes", "push", "pendingRevalidates", "catch", "err", "console", "error", "pathWasRevalidated"], "mappings": ";;;;+BAKgBA;;;eAAAA;;;AAAT,SAASA,cAAcC,GAAW;IACvC,MAAMC,+BAA+B,AACnCC,MACAC,oBAAoB,oBAFe,AACnCD,MACAC,oBAAoB,MADpBD;IAGF,MAAME,QACJH,gDAAAA,6BAA8BI,QAAQ;IAExC,IAAI,CAACD,SAAS,CAACA,MAAME,gBAAgB,EAAE;QACrC,MAAM,IAAIC,MACR,CAAC,4DAA4D,EAAEP,IAAI,CAAC;IAExE;IACA,IAAI,CAACI,MAAMI,eAAe,EAAE;QAC1BJ,MAAMI,eAAe,GAAG,EAAE;IAC5B;IACA,IAAI,CAACJ,MAAMI,eAAe,CAACC,QAAQ,CAACT,MAAM;QACxCI,MAAMI,eAAe,CAACE,IAAI,CAACV;IAC7B;IAEA,IAAI,CAACI,MAAMO,kBAAkB,EAAE;QAC7BP,MAAMO,kBAAkB,GAAG,EAAE;IAC/B;IACAP,MAAMO,kBAAkB,CAACD,IAAI,CAC3BN,MAAME,gBAAgB,CAACP,aAAa,oBAApCK,MAAME,gBAAgB,CAACP,aAAa,MAApCK,MAAME,gBAAgB,EAAiBN,KAAKY,KAAK,CAAC,CAACC;QACjDC,QAAQC,KAAK,CAAC,CAAC,yBAAyB,EAAEf,IAAI,CAAC,EAAEa;IACnD;IAGF,4CAA4C;IAC5CT,MAAMY,kBAAkB,GAAG;AAC7B"}