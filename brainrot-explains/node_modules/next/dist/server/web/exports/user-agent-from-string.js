// This file is for modularized imports for next/server to get fully-treeshaking.
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "default", {
    enumerable: true,
    get: function() {
        return _useragent.userAgentFromString;
    }
});
const _useragent = require("../spec-extension/user-agent");

//# sourceMappingURL=user-agent-from-string.js.map