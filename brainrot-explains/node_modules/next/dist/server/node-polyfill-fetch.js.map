{"version": 3, "sources": ["../../src/server/node-polyfill-fetch.ts"], "names": ["fetch", "globalThis", "getFetchImpl", "require", "getRequestImpl", "OriginalRequest", "Request", "constructor", "input", "init", "next", "args", "global", "fetchImpl", "__NEXT_HTTP_AGENT_OPTIONS", "keepAlive", "__NEXT_UNDICI_AGENT_SET", "setGlobalDis<PERSON><PERSON><PERSON>", "Agent", "pipelining", "console", "warn", "Object", "defineProperties", "Headers", "get", "Response"], "mappings": "AAAA,kEAAkE;;AAElE,IAAI,OAAOA,UAAU,eAAe,OAAOC,WAAWD,KAAK,KAAK,aAAa;IAC3E,SAASE;QACP,OAAOC,QAAQ;IACjB;IAEA,SAASC;QACP,MAAMC,kBAAkBH,eAAeI,OAAO;QAC9C,OAAO,cAAcD;YACnBE,YAAYC,KAAwB,EAAEC,IAAiB,CAAE;gBACvD,KAAK,CAACD,OAAOC;gBACb,IAAI,CAACC,IAAI,GAAGD,wBAAAA,KAAMC,IAAI;YACxB;QACF;IACF;IAEA,sFAAsF;IACtFT,WAAWD,KAAK,GAAG,CAAC,GAAGW;YAMlBC;QALH,MAAMC,YAAYX;QAElB,kDAAkD;QAClD,8CAA8C;QAC9C,IACE,GAACU,oCAAAA,OAAOE,yBAAyB,qBAAhCF,kCAAkCG,SAAS,KAC5C,CAACH,OAAOI,uBAAuB,EAC/B;YACAJ,OAAOI,uBAAuB,GAAG;YACjCH,UAAUI,mBAAmB,CAAC,IAAIJ,UAAUK,KAAK,CAAC;gBAAEC,YAAY;YAAE;YAClEC,QAAQC,IAAI,CACV;QAEJ;QACA,OAAOR,UAAUb,KAAK,IAAIW;IAC5B;IAEAW,OAAOC,gBAAgB,CAACX,QAAQ;QAC9BY,SAAS;YACPC;gBACE,OAAOvB,eAAesB,OAAO;YAC/B;QACF;QACAlB,SAAS;YACPmB;gBACE,OAAOrB;YACT;QACF;QACAsB,UAAU;YACRD;gBACE,OAAOvB,eAAewB,QAAQ;YAChC;QACF;IACF;AACF"}