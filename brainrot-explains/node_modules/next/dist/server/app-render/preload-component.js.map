{"version": 3, "sources": ["../../../src/server/app-render/preload-component.ts"], "names": ["preloadComponent", "Component", "props", "prev", "console", "error", "msg", "startsWith", "apply", "arguments", "result", "then", "x"], "mappings": ";;;;+BAAgBA;;;eAAAA;;;AAAT,SAASA,iBAAiBC,SAAc,EAAEC,KAAU;IACzD,MAAMC,OAAOC,QAAQC,KAAK;IAC1B,wDAAwD;IACxDD,QAAQC,KAAK,GAAG,SAAUC,GAAG;QAC3B,IAAIA,IAAIC,UAAU,CAAC,gCAAgC;QACjD,SAAS;QACX,OAAO;YACL,uCAAuC;YACvCJ,KAAKK,KAAK,CAACJ,SAASK;QACtB;IACF;IACA,IAAI;QACF,IAAIC,SAAST,UAAUC;QACvB,IAAIQ,UAAU,OAAOA,OAAOC,IAAI,KAAK,YAAY;YAC/C,gEAAgE;YAChED,OAAOC,IAAI,CACT,KAAO,GACP,KAAO;QAEX;QACA,OAAO;YACL,mDAAmD;YACnD,OAAOD;QACT;IACF,EAAE,OAAOE,GAAG;IACV,kDAAkD;IACpD,SAAU;QACRR,QAAQC,KAAK,GAAGF;IAClB;IACA,OAAOF;AACT"}