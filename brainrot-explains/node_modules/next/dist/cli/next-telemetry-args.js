"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "validArgs", {
    enumerable: true,
    get: function() {
        return validArgs;
    }
});
const validArgs = {
    // Types
    "--enable": <PERSON><PERSON>an,
    "--disable": <PERSON><PERSON><PERSON>,
    "--help": <PERSON><PERSON>an,
    // Aliases
    "-h": "--help"
};

//# sourceMappingURL=next-telemetry-args.js.map