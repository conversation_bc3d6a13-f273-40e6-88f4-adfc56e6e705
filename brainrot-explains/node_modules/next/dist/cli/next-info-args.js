"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "validArgs", {
    enumerable: true,
    get: function() {
        return validArgs;
    }
});
const validArgs = {
    // Types
    "--help": Boolean,
    // Aliases
    "-h": "--help",
    // Detailed diagnostics
    "--verbose": <PERSON><PERSON>an
};

//# sourceMappingURL=next-info-args.js.map