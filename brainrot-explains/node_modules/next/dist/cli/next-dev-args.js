"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "validArgs", {
    enumerable: true,
    get: function() {
        return validArgs;
    }
});
const validArgs = {
    // Types
    "--help": <PERSON><PERSON><PERSON>,
    "--port": Number,
    "--hostname": String,
    "--turbo": <PERSON><PERSON><PERSON>,
    "--experimental-https": <PERSON><PERSON>an,
    "--experimental-https-key": String,
    "--experimental-https-cert": String,
    "--experimental-https-ca": String,
    "--experimental-test-proxy": <PERSON><PERSON><PERSON>,
    "--experimental-upload-trace": String,
    // To align current messages with native binary.
    // Will need to adjust subcommand later.
    "--show-all": <PERSON><PERSON><PERSON>,
    "--root": String,
    // Aliases
    "-h": "--help",
    "-p": "--port",
    "-H": "--hostname"
};

//# sourceMappingURL=next-dev-args.js.map