{"version": 3, "sources": ["../../../src/shared/lib/base64-arraybuffer.ts"], "names": ["encode", "decode", "chars", "lookup", "Uint8Array", "i", "length", "charCodeAt", "arraybuffer", "bytes", "len", "base64", "substring", "bufferLength", "p", "encoded1", "encoded2", "encoded3", "encoded4", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": "AAAA,2FAA2F;;;;;;;;;;;;;;;;IAU9EA,MAAM;eAANA;;IAsBAC,MAAM;eAANA;;;AA9Bb,MAAMC,QAAQ;AAEd,wCAAwC;AACxC,MAAMC,SAAS,OAAOC,eAAe,cAAc,EAAE,GAAG,IAAIA,WAAW;AACvE,IAAK,IAAIC,IAAI,GAAGA,IAAIH,MAAMI,MAAM,EAAED,IAAK;IACrCF,MAAM,CAACD,MAAMK,UAAU,CAACF,GAAG,GAAGA;AAChC;AAEO,MAAML,SAAS,CAACQ;IACrB,IAAIC,QAAQ,IAAIL,WAAWI,cACzBH,GACAK,MAAMD,MAAMH,MAAM,EAClBK,SAAS;IAEX,IAAKN,IAAI,GAAGA,IAAIK,KAAKL,KAAK,EAAG;QAC3BM,UAAUT,KAAK,CAACO,KAAK,CAACJ,EAAE,IAAI,EAAE;QAC9BM,UAAUT,KAAK,CAAC,AAAEO,CAAAA,KAAK,CAACJ,EAAE,GAAG,CAAA,KAAM,IAAMI,KAAK,CAACJ,IAAI,EAAE,IAAI,EAAG;QAC5DM,UAAUT,KAAK,CAAC,AAAEO,CAAAA,KAAK,CAACJ,IAAI,EAAE,GAAG,EAAC,KAAM,IAAMI,KAAK,CAACJ,IAAI,EAAE,IAAI,EAAG;QACjEM,UAAUT,KAAK,CAACO,KAAK,CAACJ,IAAI,EAAE,GAAG,GAAG;IACpC;IAEA,IAAIK,MAAM,MAAM,GAAG;QACjBC,SAASA,OAAOC,SAAS,CAAC,GAAGD,OAAOL,MAAM,GAAG,KAAK;IACpD,OAAO,IAAII,MAAM,MAAM,GAAG;QACxBC,SAASA,OAAOC,SAAS,CAAC,GAAGD,OAAOL,MAAM,GAAG,KAAK;IACpD;IAEA,OAAOK;AACT;AAEO,MAAMV,SAAS,CAACU;IACrB,IAAIE,eAAeF,OAAOL,MAAM,GAAG,MACjCI,MAAMC,OAAOL,MAAM,EACnBD,GACAS,IAAI,GACJC,UACAC,UACAC,UACAC;IAEF,IAAIP,MAAM,CAACA,OAAOL,MAAM,GAAG,EAAE,KAAK,KAAK;QACrCO;QACA,IAAIF,MAAM,CAACA,OAAOL,MAAM,GAAG,EAAE,KAAK,KAAK;YACrCO;QACF;IACF;IAEA,MAAML,cAAc,IAAIW,YAAYN,eAClCJ,QAAQ,IAAIL,WAAWI;IAEzB,IAAKH,IAAI,GAAGA,IAAIK,KAAKL,KAAK,EAAG;QAC3BU,WAAWZ,MAAM,CAACQ,OAAOJ,UAAU,CAACF,GAAG;QACvCW,WAAWb,MAAM,CAACQ,OAAOJ,UAAU,CAACF,IAAI,GAAG;QAC3CY,WAAWd,MAAM,CAACQ,OAAOJ,UAAU,CAACF,IAAI,GAAG;QAC3Ca,WAAWf,MAAM,CAACQ,OAAOJ,UAAU,CAACF,IAAI,GAAG;QAE3CI,KAAK,CAACK,IAAI,GAAG,AAACC,YAAY,IAAMC,YAAY;QAC5CP,KAAK,CAACK,IAAI,GAAG,AAAEE,CAAAA,WAAW,EAAC,KAAM,IAAMC,YAAY;QACnDR,KAAK,CAACK,IAAI,GAAG,AAAEG,CAAAA,WAAW,CAAA,KAAM,IAAMC,WAAW;IACnD;IAEA,OAAOV;AACT"}