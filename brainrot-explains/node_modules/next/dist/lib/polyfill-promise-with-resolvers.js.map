{"version": 3, "sources": ["../../src/lib/polyfill-promise-with-resolvers.ts"], "names": ["Promise", "withResolvers", "resolvers", "promise", "resolve", "reject"], "mappings": "AAAA,+EAA+E;AAC/E,YAAY;AACZ,EAAE;AACF,+DAA+D;AAC/D,EAAE;AACF,mDAAmD;AACnD,EAAE;;AACF,IACE,CAAE,CAAA,mBAAmBA,OAAM,KAC3B,OAAOA,QAAQC,aAAa,KAAK,YACjC;IACAD,QAAQC,aAAa,GAAG;QAKtB,IAAIC;QAKJ,6DAA6D;QAC7D,MAAMC,UAAU,IAAIH,QAAW,CAACI,SAASC;YACvCH,YAAY;gBAAEE;gBAASC;YAAO;QAChC;QAEA,yEAAyE;QACzE,iBAAiB;QACjB,OAAO;YAAEF;YAASC,SAASF,UAAWE,OAAO;YAAEC,QAAQH,UAAWG,MAAM;QAAC;IAC3E;AACF"}