import { Worker as Jest<PERSON>orker } from 'next/dist/compiled/jest-worker';
type FarmOptions = ConstructorParameters<typeof JestWorker>[1];
export declare class Worker {
    private _worker;
    constructor(workerPath: string, options: FarmOptions & {
        timeout?: number;
        onRestart?: (method: string, args: any[], attempts: number) => void;
        exposedMethods: ReadonlyArray<string>;
        enableWorkerThreads?: boolean;
    });
    end(): ReturnType<JestWorker['end']>;
    /**
     * Quietly end the worker if it exists
     */
    close(): void;
}
export {};
