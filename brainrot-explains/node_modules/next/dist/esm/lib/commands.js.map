{"version": 3, "sources": ["../../src/lib/commands.ts"], "names": ["commands", "build", "Promise", "resolve", "require", "nextBuild", "start", "nextStart", "export", "nextExport", "dev", "nextDev", "lint", "nextLint", "telemetry", "nextTelemetry", "info", "nextInfo"], "mappings": "AAIA,OAAO,MAAMA,WAA6D;IACxEC,OAAO,IAAMC,QAAQC,OAAO,CAACC,QAAQ,qBAAqBC,SAAS;IACnEC,OAAO,IAAMJ,QAAQC,OAAO,CAACC,QAAQ,qBAAqBG,SAAS;IACnEC,QAAQ,IAAMN,QAAQC,OAAO,CAACC,QAAQ,sBAAsBK,UAAU;IACtEC,KAAK,IAAMR,QAAQC,OAAO,CAACC,QAAQ,mBAAmBO,OAAO;IAC7DC,MAAM,IAAMV,QAAQC,OAAO,CAACC,QAAQ,oBAAoBS,QAAQ;IAChEC,WAAW,IACTZ,QAAQC,OAAO,CAACC,QAAQ,yBAAyBW,aAAa;IAChEC,MAAM,IAAMd,QAAQC,OAAO,CAACC,QAAQ,oBAAoBa,QAAQ;IAChE,wBAAwB,IACtBf,QAAQC,OAAO,CAACC,QAAQ,qBAAqBC,SAAS;IACxD,yBAAyB,IACvBH,QAAQC,OAAO,CAACC,QAAQ,qBAAqBC,SAAS;AAC1D,EAAC"}