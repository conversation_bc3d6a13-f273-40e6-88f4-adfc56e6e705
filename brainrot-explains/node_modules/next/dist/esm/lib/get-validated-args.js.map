{"version": 3, "sources": ["../../src/lib/get-validated-args.ts"], "names": ["arg", "printAndExit", "isError", "getValidatedArgs", "validArgs", "argv", "args", "error", "code", "message"], "mappings": "AAAA,OAAOA,SAAS,kCAAiC;AACjD,SAASC,YAAY,QAAQ,sBAAqB;AAClD,OAAOC,aAAa,aAAY;AAEhC,OAAO,SAASC,iBAAiBC,SAAmB,EAAEC,IAAe;IACnE,IAAIC;IACJ,IAAI;QACFA,OAAON,IAAII,WAAW;YAAEC;QAAK;IAC/B,EAAE,OAAOE,OAAO;QACd,IAAIL,QAAQK,UAAUA,MAAMC,IAAI,KAAK,sBAAsB;YACzDP,aAAaM,MAAME,OAAO,EAAE;QAC9B;QACA,MAAMF;IACR;IACA,OAAOD;AACT"}