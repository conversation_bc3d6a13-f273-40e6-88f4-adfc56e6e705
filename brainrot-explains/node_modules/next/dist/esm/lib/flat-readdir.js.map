{"version": 3, "sources": ["../../src/lib/flat-readdir.ts"], "names": ["join", "fs", "flatReaddir", "dir", "includes", "dirents", "opendir", "result", "part", "shouldOmit", "isDirectory", "some", "include", "test", "name", "isSymbolicLink", "stats", "stat", "push"], "mappings": "AAAA,SAASA,IAAI,QAAQ,OAAM;AAC3B,OAAOC,QAAQ,cAAa;AAE5B,OAAO,eAAeC,YAAYC,GAAW,EAAEC,QAAkB;IAC/D,MAAMC,UAAU,MAAMJ,GAAGK,OAAO,CAACH;IACjC,MAAMI,SAAS,EAAE;IAEjB,WAAW,MAAMC,QAAQH,QAAS;QAChC,IAAII,aACFD,KAAKE,WAAW,MAAM,CAACN,SAASO,IAAI,CAAC,CAACC,UAAYA,QAAQC,IAAI,CAACL,KAAKM,IAAI;QAE1E,IAAIN,KAAKO,cAAc,IAAI;YACzB,MAAMC,QAAQ,MAAMf,GAAGgB,IAAI,CAACjB,KAAKG,KAAKK,KAAKM,IAAI;YAC/CL,aAAaO,MAAMN,WAAW;QAChC;QAEA,IAAI,CAACD,YAAY;YACfF,OAAOW,IAAI,CAAClB,KAAKG,KAAKK,KAAKM,IAAI;QACjC;IACF;IAEA,OAAOP;AACT"}