{"version": 3, "sources": ["../../../src/client/components/client-hook-in-server-component-error.ts"], "names": ["React", "clientHookInServerComponentError", "<PERSON><PERSON><PERSON>", "process", "env", "NODE_ENV", "useState", "Error"], "mappings": "AAAA,OAAOA,WAAW,QAAO;AAEzB,OAAO,SAASC,iCACdC,QAAgB;IAEhB,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,uDAAuD;QACvD,IAAI,CAACL,MAAMM,QAAQ,EAAE;YACnB,MAAM,IAAIC,MACR,AAAC,KAAEL,WAAS;QAEhB;IACF;AACF"}