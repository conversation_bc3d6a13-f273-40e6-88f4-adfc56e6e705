{"version": 3, "sources": ["../../../../src/client/components/router-reducer/get-prefetch-cache-entry-status.ts"], "names": ["FIVE_MINUTES", "THIRTY_SECONDS", "PrefetchCacheEntryStatus", "fresh", "reusable", "expired", "stale", "getPrefetchEntryCacheStatus", "kind", "prefetchTime", "lastUsedTime", "Date", "now"], "mappings": "AAEA,MAAMA,eAAe,IAAI,KAAK;AAC9B,MAAMC,iBAAiB,KAAK;WAErB;UAAKC,wBAAwB;IAAxBA,yBACVC,WAAAA;IADUD,yBAEVE,cAAAA;IAFUF,yBAGVG,aAAAA;IAHUH,yBAIVI,WAAAA;GAJUJ,6BAAAA;AAOZ,OAAO,SAASK,4BAA4B,KAIvB;IAJuB,IAAA,EAC1CC,IAAI,EACJC,YAAY,EACZC,YAAY,EACO,GAJuB;IAK1C,yFAAyF;IACzF,IAAIC,KAAKC,GAAG,KAAK,AAACF,CAAAA,uBAAAA,eAAgBD,YAAW,IAAKR,gBAAgB;QAChE,OAAOS,eAZE,aADH;IAgBR;IAEA,wGAAwG;IACxG,IAAIF,SAAS,QAAQ;QACnB,IAAIG,KAAKC,GAAG,KAAKH,eAAeT,cAAc;YAC5C,OAlBI;QAmBN;IACF;IAEA,oHAAoH;IACpH,IAAIQ,SAAS,QAAQ;QACnB,IAAIG,KAAKC,GAAG,KAAKH,eAAeT,cAAc;YAC5C,OA3BO;QA4BT;IACF;IAEA,OA9BU;AA+BZ"}