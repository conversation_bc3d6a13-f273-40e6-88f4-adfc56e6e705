{"version": 3, "sources": ["../../../../src/client/components/router-reducer/create-optimistic-tree.ts"], "names": ["matchSegment", "createOptimisticTree", "segments", "flightRouterState", "parentRefetch", "existingSegment", "existingParallelRoutes", "url", "refresh", "isRootLayout", "segment", "isLastSegment", "length", "segmentMatches", "hasMultipleParallelRoutes", "Object", "keys", "shouldRefetchThisLevel", "parallelRoutes", "childTree", "childItem", "slice", "children", "result"], "mappings": "AACA,SAASA,YAAY,QAAQ,oBAAmB;AAEhD;;;CAGC,GACD,OAAO,SAASC,qBACdC,QAAkB,EAClBC,iBAA2C,EAC3CC,aAAsB;IAEtB,MAAM,CAACC,iBAAiBC,wBAAwBC,KAAKC,SAASC,aAAa,GACzEN,qBAAqB;QAAC;QAAM,CAAC;KAAE;IACjC,MAAMO,UAAUR,QAAQ,CAAC,EAAE;IAC3B,MAAMS,gBAAgBT,SAASU,MAAM,KAAK;IAE1C,MAAMC,iBACJR,oBAAoB,QAAQL,aAAaK,iBAAiBK;IAE5D,+EAA+E;IAC/E,yEAAyE;IACzE,8CAA8C;IAC9C,MAAMI,4BACJC,OAAOC,IAAI,CAACV,wBAAwBM,MAAM,GAAG;IAC/C,MAAMK,yBACJ,CAACd,qBAAqB,CAACU,kBAAkBC;IAE3C,IAAII,iBAAuC,CAAC;IAC5C,IAAIb,oBAAoB,QAAQQ,gBAAgB;QAC9CK,iBAAiBZ;IACnB;IAEA,IAAIa;IAEJ,4EAA4E;IAC5E,0EAA0E;IAC1E,0BAA0B;IAC1B,IAAI,CAACR,iBAAiB,CAACG,2BAA2B;QAChD,MAAMM,YAAYnB,qBAChBC,SAASmB,KAAK,CAAC,IACfH,iBAAiBA,eAAeI,QAAQ,GAAG,MAC3ClB,iBAAiBa;QAGnBE,YAAYC;IACd;IAEA,MAAMG,SAA4B;QAChCb;QACA;YACE,GAAGQ,cAAc;YACjB,GAAIC,YAAY;gBAAEG,UAAUH;YAAU,IAAI,CAAC,CAAC;QAC9C;KACD;IAED,IAAIZ,KAAK;QACPgB,MAAM,CAAC,EAAE,GAAGhB;IACd;IAEA,IAAI,CAACH,iBAAiBa,wBAAwB;QAC5CM,MAAM,CAAC,EAAE,GAAG;IACd,OAAO,IAAIV,kBAAkBL,SAAS;QACpCe,MAAM,CAAC,EAAE,GAAGf;IACd;IAEA,IAAIK,kBAAkBJ,cAAc;QAClCc,MAAM,CAAC,EAAE,GAAGd;IACd;IAEA,OAAOc;AACT"}