{"version": 3, "sources": ["../../../src/server/send-payload/index.ts"], "names": ["isResSent", "generateETag", "fresh", "setRevalidateHeaders", "RSC_CONTENT_TYPE_HEADER", "sendEtagResponse", "req", "res", "etag", "<PERSON><PERSON><PERSON><PERSON>", "headers", "statusCode", "end", "sendRenderResult", "result", "type", "generateEtags", "poweredByHeader", "options", "payload", "isDynamic", "toUnchunkedString", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "contentType", "<PERSON><PERSON><PERSON>", "byteLength", "method", "pipe"], "mappings": "AAEA,SAASA,SAAS,QAAQ,yBAAwB;AAClD,SAASC,YAAY,QAAQ,cAAa;AAC1C,OAAOC,WAAW,2BAA0B;AAE5C,SAASC,oBAAoB,QAAQ,uBAAsB;AAC3D,SAASC,uBAAuB,QAAQ,6CAA4C;AAOpF,SAASD,oBAAoB,GAAE;AAE/B,OAAO,SAASE,iBACdC,GAAoB,EACpBC,GAAmB,EACnBC,IAAwB;IAExB,IAAIA,MAAM;QACR;;;;;KAKC,GACDD,IAAIE,SAAS,CAAC,QAAQD;IACxB;IAEA,IAAIN,MAAMI,IAAII,OAAO,EAAE;QAAEF;IAAK,IAAI;QAChCD,IAAII,UAAU,GAAG;QACjBJ,IAAIK,GAAG;QACP,OAAO;IACT;IAEA,OAAO;AACT;AAEA,OAAO,eAAeC,iBAAiB,EACrCP,GAAG,EACHC,GAAG,EACHO,MAAM,EACNC,IAAI,EACJC,aAAa,EACbC,eAAe,EACfC,OAAO,EASR;IACC,IAAIlB,UAAUO,MAAM;QAClB;IACF;IAEA,IAAIU,mBAAmBF,SAAS,QAAQ;QACtCR,IAAIE,SAAS,CAAC,gBAAgB;IAChC;IAEA,IAAIS,WAAW,MAAM;QACnBf,qBAAqBI,KAAKW;IAC5B;IAEA,MAAMC,UAAUL,OAAOM,SAAS,GAAG,OAAO,MAAMN,OAAOO,iBAAiB;IAExE,IAAIF,YAAY,MAAM;QACpB,MAAMX,OAAOQ,gBAAgBf,aAAakB,WAAWG;QACrD,IAAIjB,iBAAiBC,KAAKC,KAAKC,OAAO;YACpC;QACF;IACF;IAEA,IAAI,CAACD,IAAIgB,SAAS,CAAC,iBAAiB;QAClChB,IAAIE,SAAS,CACX,gBACAK,OAAOU,WAAW,GACdV,OAAOU,WAAW,GAClBT,SAAS,QACTX,0BACAW,SAAS,SACT,qBACA;IAER;IAEA,IAAII,SAAS;QACXZ,IAAIE,SAAS,CAAC,kBAAkBgB,OAAOC,UAAU,CAACP;IACpD;IAEA,IAAIb,IAAIqB,MAAM,KAAK,QAAQ;QACzBpB,IAAIK,GAAG,CAAC;IACV,OAAO,IAAIO,YAAY,MAAM;QAC3BZ,IAAIK,GAAG,CAACO;IACV,OAAO;QACL,MAAML,OAAOc,IAAI,CAACrB;IACpB;AACF"}