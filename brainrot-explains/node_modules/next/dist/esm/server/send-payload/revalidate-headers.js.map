{"version": 3, "sources": ["../../../src/server/send-payload/revalidate-headers.ts"], "names": ["setRevalidateHeaders", "res", "options", "private", "stateful", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "revalidate", "Error"], "mappings": "AAIA,OAAO,SAASA,qBACdC,GAAsC,EACtCC,OAAuB;IAEvB,IAAIA,QAAQC,OAAO,IAAID,QAAQE,QAAQ,EAAE;QACvC,IAAIF,QAAQC,OAAO,IAAI,CAACF,IAAII,SAAS,CAAC,kBAAkB;YACtDJ,IAAIK,SAAS,CACX,iBACA,CAAC,uDAAuD,CAAC;QAE7D;IACF,OAAO,IAAI,OAAOJ,QAAQK,UAAU,KAAK,UAAU;QACjD,IAAIL,QAAQK,UAAU,GAAG,GAAG;YAC1B,MAAM,IAAIC,MACR,CAAC,oDAAoD,EAAEN,QAAQK,UAAU,CAAC,IAAI,CAAC;QAEnF;QAEAN,IAAIK,SAAS,CACX,iBACA,CAAC,SAAS,EAAEJ,QAAQK,UAAU,CAAC,wBAAwB,CAAC;IAE5D,OAAO,IAAIL,QAAQK,UAAU,KAAK,OAAO;QACvCN,IAAIK,SAAS,CAAC,iBAAiB,CAAC,yCAAyC,CAAC;IAC5E;AACF"}