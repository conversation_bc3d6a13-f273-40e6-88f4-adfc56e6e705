{"version": 3, "sources": ["../../../src/server/app-render/create-server-components-renderer.tsx"], "names": ["React", "use", "useFlightResponse", "createServerComponentRenderer", "ComponentToRender", "ComponentMod", "inlinedDataTransformStream", "clientReferenceManifest", "serverContexts", "formState", "serverComponentsErrorHandler", "nonce", "flightStream", "createFlightStream", "props", "renderToReadableStream", "clientModules", "context", "onError", "flightResponseRef", "current", "writable", "ServerComponentWrapper", "response"], "mappings": "AAIA,OAAOA,SAASC,GAAG,QAAQ,QAAO;AAElC,SAASC,iBAAiB,QAAQ,wBAAuB;AAEzD;;;CAGC,GACD,OAAO,SAASC,8BACdC,iBAAwC,EACxCC,YAA2B,EAC3B,EACEC,0BAA0B,EAC1BC,uBAAuB,EACvBC,cAAc,EACdC,SAAS,EAQV,EACDC,4BAAmE,EACnEC,KAAc;IAEd,IAAIC;IACJ,MAAMC,qBAAqB,CAACC;QAC1B,IAAI,CAACF,cAAc;YACjBA,eAAeP,aAAaU,sBAAsB,eAChD,oBAACX,mBAAuBU,QACxBP,wBAAwBS,aAAa,EACrC;gBACEC,SAAST;gBACTU,SAASR;YACX;QAEJ;QACA,OAAOE;IACT;IAEA,MAAMO,oBAAuC;QAAEC,SAAS;IAAK;IAE7D,MAAMC,WAAWf,2BAA2Be,QAAQ;IACpD,OAAO,SAASC,uBAAuBR,KAAY;QACjD,MAAMS,WAAWrB,kBACfmB,UACAR,mBAAmBC,QACnBP,yBACAY,mBACAV,WACAE;QAEF,OAAOV,IAAIsB;IACb;AACF"}