// Polyfill Web Streams for the Node.js runtime.
if (!global.ReadableStream) {
    // In Node v16, ReadableStream is available natively but under the `stream` namespace.
    // In Node v18+, it's available under global.
    if (require("stream/web").ReadableStream) {
        global.ReadableStream = require("stream/web").ReadableStream;
    } else {
        const { ReadableStream } = require("next/dist/compiled/@edge-runtime/ponyfill");
        global.ReadableStream = ReadableStream;
    }
}
if (!global.TransformStream) {
    // Same as ReadableStream above.
    if (require("stream/web").TransformStream) {
        global.TransformStream = require("stream/web").TransformStream;
    } else {
        const { TransformStream } = require("next/dist/compiled/@edge-runtime/ponyfill");
        global.TransformStream = TransformStream;
    }
}

//# sourceMappingURL=node-polyfill-web-streams.js.map