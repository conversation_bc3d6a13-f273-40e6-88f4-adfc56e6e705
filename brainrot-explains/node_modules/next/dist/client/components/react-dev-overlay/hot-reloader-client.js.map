{"version": 3, "sources": ["../../../../src/client/components/react-dev-overlay/hot-reloader-client.tsx"], "names": ["HotReload", "mostRecentCompilationHash", "__nextDevClientId", "Math", "round", "random", "Date", "now", "reloading", "onBeforeFastRefresh", "dispatcher", "hasUpdates", "onBeforeRefresh", "onFastRefresh", "onBuildOk", "onRefresh", "handleAvailableHash", "hash", "isUpdateAvailable", "__webpack_hash__", "canApplyUpdates", "module", "hot", "status", "afterApplyUpdates", "fn", "handler", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addStatusHandler", "performFullReload", "err", "sendMessage", "stackTrace", "stack", "split", "slice", "join", "message", "JSON", "stringify", "event", "hadRuntimeError", "RuntimeError<PERSON>andler", "window", "location", "reload", "tryApplyUpdates", "onBeforeUpdate", "onHotUpdateSuccess", "handleApplyUpdates", "updatedModules", "console", "warn", "Boolean", "length", "process", "env", "__NEXT_TEST_MODE", "self", "__NEXT_HMR_CB", "check", "then", "apply", "processMessage", "obj", "router", "handleErrors", "errors", "formatted", "formatWebpackMessages", "warnings", "onBuildError", "i", "error", "stripAnsi", "action", "HMR_ACTIONS_SENT_TO_BROWSER", "BUILDING", "log", "BUILT", "SYNC", "onVersionInfo", "versionInfo", "hasErrors", "errorCount", "clientId", "hasWarnings", "warningCount", "isHotUpdate", "formattedMessages", "onBeforeHotUpdate", "onSuccessfulHotUpdate", "__NEXT_DATA__", "page", "SERVER_COMPONENT_CHANGES", "startTransition", "fastRefresh", "RELOAD_PAGE", "REMOVED_PAGE", "ADDED_PAGE", "SERVER_ERROR", "errorJSON", "parse", "Error", "DEV_PAGES_MANIFEST_UPDATE", "assetPrefix", "children", "state", "dispatch", "useReducer", "errorOverlayReducer", "INITIAL_OVERLAY_STATE", "useMemo", "type", "ACTION_BUILD_OK", "ACTION_BUILD_ERROR", "ACTION_BEFORE_REFRESH", "ACTION_REFRESH", "ACTION_VERSION_INFO", "handleOnUnhandledError", "useCallback", "componentStack", "_componentStack", "ACTION_UNHANDLED_ERROR", "reason", "frames", "parseStack", "componentStackFrames", "parseComponentStack", "handleOnUnhandledRejection", "ACTION_UNHANDLED_REJECTION", "handleOnReactError", "useErrorHandler", "webSocketRef", "useWebsocket", "useWebsocketPing", "useSendMessage", "processTurbopackMessage", "useTurbopack", "useRouter", "useEffect", "data", "handledByTurbopack", "websocket", "current", "addEventListener", "removeEventListener", "ReactDevOverlay", "onReactError"], "mappings": ";;;;+BAqaA;;;eAAwBA;;;;;iEA9ZjB;oEACe;gFACY;4BACR;qCAKnB;4BASoB;0EACC;iCAIrB;8BAMA;qCAC6B;kCAEQ;AAW5C,IAAIC,4BAAiC;AACrC,IAAIC,oBAAoBC,KAAKC,KAAK,CAACD,KAAKE,MAAM,KAAK,MAAMC,KAAKC,GAAG;AACjE,IAAIC,YAAY;AAEhB,SAASC,oBAAoBC,UAAsB,EAAEC,UAAmB;IACtE,IAAIA,YAAY;QACdD,WAAWE,eAAe;IAC5B;AACF;AAEA,SAASC,cAAcH,UAAsB,EAAEC,UAAmB;IAChED,WAAWI,SAAS;IACpB,IAAIH,YAAY;QACdD,WAAWK,SAAS;IACtB;AACF;AAEA,kDAAkD;AAClD,SAASC,oBAAoBC,IAAY;IACvC,sCAAsC;IACtChB,4BAA4BgB;AAC9B;AAEA,mDAAmD;AACnD,SAASC;IACP,4BAA4B,GAC5B,2DAA2D;IAC3D,8CAA8C;IAC9C,OAAOjB,8BAA8BkB;AACvC;AAEA,6CAA6C;AAC7C,SAASC;IACP,qCAAqC;IACrC,OAAOC,OAAOC,GAAG,CAACC,MAAM,OAAO;AACjC;AACA,SAASC,kBAAkBC,EAAO;IAChC,IAAIL,mBAAmB;QACrBK;IACF,OAAO;QACL,SAASC,QAAQH,MAAW;YAC1B,IAAIA,WAAW,QAAQ;gBACrB,qCAAqC;gBACrCF,OAAOC,GAAG,CAACK,mBAAmB,CAACD;gBAC/BD;YACF;QACF;QACA,qCAAqC;QACrCJ,OAAOC,GAAG,CAACM,gBAAgB,CAACF;IAC9B;AACF;AAEA,SAASG,kBAAkBC,GAAQ,EAAEC,WAAgB;IACnD,MAAMC,aACJF,OACC,CAAA,AAACA,IAAIG,KAAK,IAAIH,IAAIG,KAAK,CAACC,KAAK,CAAC,MAAMC,KAAK,CAAC,GAAG,GAAGC,IAAI,CAAC,SACpDN,IAAIO,OAAO,IACXP,MAAM,EAAC;IAEXC,YACEO,KAAKC,SAAS,CAAC;QACbC,OAAO;QACPR;QACAS,iBAAiB,CAAC,CAACC,oCAAmB,CAACD,eAAe;IACxD;IAGF,IAAIjC,WAAW;IACfA,YAAY;IACZmC,OAAOC,QAAQ,CAACC,MAAM;AACxB;AAEA,iEAAiE;AACjE,SAASC,gBACPC,cAA6C,EAC7CC,kBAAiD,EACjDjB,WAAgB,EAChBrB,UAAsB;IAEtB,IAAI,CAACQ,uBAAuB,CAACE,mBAAmB;QAC9CV,WAAWI,SAAS;QACpB;IACF;IAEA,SAASmC,mBAAmBnB,GAAQ,EAAEoB,cAA4B;QAChE,IAAIpB,OAAOY,oCAAmB,CAACD,eAAe,IAAI,CAACS,gBAAgB;YACjE,IAAIpB,KAAK;gBACPqB,QAAQC,IAAI,CACV,8CACE,mIACA,qIACA,+GACA,8HACA;YAEN,OAAO,IAAIV,oCAAmB,CAACD,eAAe,EAAE;gBAC9CU,QAAQC,IAAI,CACV;YAEJ;YACAvB,kBAAkBC,KAAKC;YACvB;QACF;QAEA,MAAMpB,aAAa0C,QAAQH,eAAeI,MAAM;QAChD,IAAI,OAAON,uBAAuB,YAAY;YAC5C,iCAAiC;YACjCA,mBAAmBrC;QACrB;QAEA,IAAIO,qBAAqB;YACvB,+DAA+D;YAC/D4B,gBACEnC,aAAa,KAAO,IAAIoC,gBACxBpC,aAAa,IAAMD,WAAWI,SAAS,KAAKkC,oBAC5CjB,aACArB;QAEJ,OAAO;YACLA,WAAWI,SAAS;YACpB,IAAIyC,QAAQC,GAAG,CAACC,gBAAgB,EAAE;gBAChCjC,kBAAkB;oBAChB,IAAIkC,KAAKC,aAAa,EAAE;wBACtBD,KAAKC,aAAa;wBAClBD,KAAKC,aAAa,GAAG;oBACvB;gBACF;YACF;QACF;IACF;IAEA,2DAA2D;IAC3D,qCAAqC;IACrCtC,OAAOC,GAAG,CACPsC,KAAK,CAAC,aAAa,GAAG,OACtBC,IAAI,CAAC,CAACX;QACL,IAAI,CAACA,gBAAgB;YACnB,OAAO;QACT;QAEA,IAAI,OAAOH,mBAAmB,YAAY;YACxC,MAAMpC,aAAa0C,QAAQH,eAAeI,MAAM;YAChDP,eAAepC;QACjB;QACA,2DAA2D;QAC3D,qCAAqC;QACrC,OAAOU,OAAOC,GAAG,CAACwC,KAAK;IACzB,GACCD,IAAI,CACH,CAACX;QACCD,mBAAmB,MAAMC;IAC3B,GACA,CAACpB;QACCmB,mBAAmBnB,KAAK;IAC1B;AAEN;AAEA,SAASiC,eACPC,GAAqB,EACrBjC,WAAgB,EAChBkC,MAAoC,EACpCvD,UAAsB;IAEtB,IAAI,CAAE,CAAA,YAAYsD,GAAE,GAAI;QACtB;IACF;IAEA,SAASE,aAAaC,MAA8B;QAClD,8BAA8B;QAC9B,MAAMC,YAAYC,IAAAA,8BAAqB,EAAC;YACtCF,QAAQA;YACRG,UAAU,EAAE;QACd;QAEA,6BAA6B;QAC7B5D,WAAW6D,YAAY,CAACH,UAAUD,MAAM,CAAC,EAAE;QAE3C,gCAAgC;QAChC,IAAK,IAAIK,IAAI,GAAGA,IAAIJ,UAAUD,MAAM,CAACb,MAAM,EAAEkB,IAAK;YAChDrB,QAAQsB,KAAK,CAACC,IAAAA,kBAAS,EAACN,UAAUD,MAAM,CAACK,EAAE;QAC7C;QAEA,gCAAgC;QAChC,0CAA0C;QAC1C,IAAIjB,QAAQC,GAAG,CAACC,gBAAgB,EAAE;YAChC,IAAIC,KAAKC,aAAa,EAAE;gBACtBD,KAAKC,aAAa,CAACS,UAAUD,MAAM,CAAC,EAAE;gBACtCT,KAAKC,aAAa,GAAG;YACvB;QACF;IACF;IAEA,OAAQK,IAAIW,MAAM;QAChB,KAAKC,6CAA2B,CAACC,QAAQ;YAAE;gBACzC1B,QAAQ2B,GAAG,CAAC;gBACZ;YACF;QACA,KAAKF,6CAA2B,CAACG,KAAK;QACtC,KAAKH,6CAA2B,CAACI,IAAI;YAAE;gBACrC,IAAIhB,IAAI/C,IAAI,EAAE;oBACZD,oBAAoBgD,IAAI/C,IAAI;gBAC9B;gBAEA,MAAM,EAAEkD,MAAM,EAAEG,QAAQ,EAAE,GAAGN;gBAE7B,yCAAyC;gBACzC,IAAI,iBAAiBA,KAAK;oBACxBtD,WAAWuE,aAAa,CAACjB,IAAIkB,WAAW;gBAC1C;gBACA,MAAMC,YAAY9B,QAAQc,UAAUA,OAAOb,MAAM;gBACjD,kEAAkE;gBAClE,IAAI6B,WAAW;oBACbpD,YACEO,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACP4C,YAAYjB,OAAOb,MAAM;wBACzB+B,UAAUnF;oBACZ;oBAGFgE,aAAaC;oBACb;gBACF;gBAEA,MAAMmB,cAAcjC,QAAQiB,YAAYA,SAAShB,MAAM;gBACvD,IAAIgC,aAAa;oBACfvD,YACEO,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACP+C,cAAcjB,SAAShB,MAAM;wBAC7B+B,UAAUnF;oBACZ;oBAGF,2CAA2C;oBAC3C,MAAMsF,cAAcxB,IAAIW,MAAM,KAAKC,6CAA2B,CAACI,IAAI;oBAEnE,iCAAiC;oBACjC,MAAMS,oBAAoBpB,IAAAA,8BAAqB,EAAC;wBAC9CC,UAAUA;wBACVH,QAAQ,EAAE;oBACZ;oBAEA,IAAK,IAAIK,IAAI,GAAGA,IAAIiB,kBAAkBnB,QAAQ,CAAChB,MAAM,EAAEkB,IAAK;wBAC1D,IAAIA,MAAM,GAAG;4BACXrB,QAAQC,IAAI,CACV,+CACE;4BAEJ;wBACF;wBACAD,QAAQC,IAAI,CAACsB,IAAAA,kBAAS,EAACe,kBAAkBnB,QAAQ,CAACE,EAAE;oBACtD;oBAEA,0CAA0C;oBAC1C,IAAIgB,aAAa;wBACf1C,gBACE,SAAS4C,kBAAkB/E,UAAmB;4BAC5CF,oBAAoBC,YAAYC;wBAClC,GACA,SAASgF,sBAAsBhF,UAAe;4BAC5C,qDAAqD;4BACrD,sDAAsD;4BACtDE,cAAcH,YAAYC;wBAC5B,GACAoB,aACArB;oBAEJ;oBACA;gBACF;gBAEAqB,YACEO,KAAKC,SAAS,CAAC;oBACbC,OAAO;oBACP6C,UAAUnF;gBACZ;gBAGF,MAAMsF,cACJxB,IAAIW,MAAM,KAAKC,6CAA2B,CAACI,IAAI,IAC9C,CAAA,CAACrC,OAAOiD,aAAa,IAAIjD,OAAOiD,aAAa,CAACC,IAAI,KAAK,SAAQ,KAChE3E;gBAEF,0CAA0C;gBAC1C,IAAIsE,aAAa;oBACf1C,gBACE,SAAS4C,kBAAkB/E,UAAmB;wBAC5CF,oBAAoBC,YAAYC;oBAClC,GACA,SAASgF,sBAAsBhF,UAAe;wBAC5C,qDAAqD;wBACrD,sDAAsD;wBACtDE,cAAcH,YAAYC;oBAC5B,GACAoB,aACArB;gBAEJ;gBACA;YACF;QACA,uDAAuD;QACvD,KAAKkE,6CAA2B,CAACkB,wBAAwB;YAAE;gBACzD/D,YACEO,KAAKC,SAAS,CAAC;oBACbC,OAAO;oBACP6C,UAAUnF;gBACZ;gBAEF,IAAIwC,oCAAmB,CAACD,eAAe,EAAE;oBACvC,IAAIjC,WAAW;oBACfA,YAAY;oBACZ,OAAOmC,OAAOC,QAAQ,CAACC,MAAM;gBAC/B;gBACAkD,IAAAA,sBAAe,EAAC;oBACd,yCAAyC;oBACzC9B,OAAO+B,WAAW;oBAClBtF,WAAWK,SAAS;gBACtB;gBAEA,IAAIwC,QAAQC,GAAG,CAACC,gBAAgB,EAAE;oBAChC,IAAIC,KAAKC,aAAa,EAAE;wBACtBD,KAAKC,aAAa;wBAClBD,KAAKC,aAAa,GAAG;oBACvB;gBACF;gBAEA;YACF;QACA,KAAKiB,6CAA2B,CAACqB,WAAW;YAAE;gBAC5ClE,YACEO,KAAKC,SAAS,CAAC;oBACbC,OAAO;oBACP6C,UAAUnF;gBACZ;gBAEF,IAAIM,WAAW;gBACfA,YAAY;gBACZ,OAAOmC,OAAOC,QAAQ,CAACC,MAAM;YAC/B;QACA,KAAK+B,6CAA2B,CAACsB,YAAY;YAAE;gBAC7C,+EAA+E;gBAC/E,yCAAyC;gBACzCjC,OAAO+B,WAAW;gBAClB;YACF;QACA,KAAKpB,6CAA2B,CAACuB,UAAU;YAAE;gBAC3C,6EAA6E;gBAC7E,yCAAyC;gBACzClC,OAAO+B,WAAW;gBAClB;YACF;QACA,KAAKpB,6CAA2B,CAACwB,YAAY;YAAE;gBAC7C,MAAM,EAAEC,SAAS,EAAE,GAAGrC;gBACtB,IAAIqC,WAAW;oBACb,MAAM,EAAEhE,OAAO,EAAEJ,KAAK,EAAE,GAAGK,KAAKgE,KAAK,CAACD;oBACtC,MAAM5B,QAAQ,IAAI8B,MAAMlE;oBACxBoC,MAAMxC,KAAK,GAAGA;oBACdiC,aAAa;wBAACO;qBAAM;gBACtB;gBACA;YACF;QACA,KAAKG,6CAA2B,CAAC4B,yBAAyB;YAAE;gBAC1D;YACF;QACA;YAAS;gBACP,MAAM,IAAID,MAAM,uBAAuBjE,KAAKC,SAAS,CAACyB;YACxD;IACF;AACF;AAEe,SAAShE,UAAU,KAMjC;IANiC,IAAA,EAChCyG,WAAW,EACXC,QAAQ,EAIT,GANiC;IAOhC,MAAM,CAACC,OAAOC,SAAS,GAAGC,IAAAA,iBAAU,EAClCC,wCAAmB,EACnBC,0CAAqB;IAEvB,MAAMrG,aAAasG,IAAAA,cAAO,EAAC;QACzB,OAAO;YACLlG;gBACE8F,SAAS;oBAAEK,MAAMC,oCAAe;gBAAC;YACnC;YACA3C,cAAalC,OAAO;gBAClBuE,SAAS;oBAAEK,MAAME,uCAAkB;oBAAE9E;gBAAQ;YAC/C;YACAzB;gBACEgG,SAAS;oBAAEK,MAAMG,0CAAqB;gBAAC;YACzC;YACArG;gBACE6F,SAAS;oBAAEK,MAAMI,mCAAc;gBAAC;YAClC;YACApC,eAAcC,WAAW;gBACvB0B,SAAS;oBAAEK,MAAMK,wCAAmB;oBAAEpC;gBAAY;YACpD;QACF;IACF,GAAG;QAAC0B;KAAS;IAEb,MAAMW,yBAAyBC,IAAAA,kBAAW,EAAC,CAAC/C;QAC1C,kGAAkG;QAClG,MAAMgD,iBAAiB,AAAChD,MAAciD,eAAe;QACrDd,SAAS;YACPK,MAAMU,2CAAsB;YAC5BC,QAAQnD;YACRoD,QAAQC,IAAAA,sBAAU,EAACrD,MAAMxC,KAAK;YAC9B8F,sBACEN,kBAAkBO,IAAAA,wCAAmB,EAACP;QAC1C;IACF,GAAG,EAAE;IACL,MAAMQ,6BAA6BT,IAAAA,kBAAW,EAAC,CAACI;QAC9ChB,SAAS;YACPK,MAAMiB,+CAA0B;YAChCN,QAAQA;YACRC,QAAQC,IAAAA,sBAAU,EAACF,OAAO3F,KAAK;QACjC;IACF,GAAG,EAAE;IACL,MAAMkG,qBAAqBX,IAAAA,kBAAW,EAAC;QACrC9E,oCAAmB,CAACD,eAAe,GAAG;IACxC,GAAG,EAAE;IACL2F,IAAAA,gCAAe,EAACb,wBAAwBU;IAExC,MAAMI,eAAeC,IAAAA,0BAAY,EAAC7B;IAClC8B,IAAAA,8BAAgB,EAACF;IACjB,MAAMtG,cAAcyG,IAAAA,4BAAc,EAACH;IACnC,MAAMI,0BAA0BC,IAAAA,0BAAY,EAAC3G;IAE7C,MAAMkC,SAAS0E,IAAAA,qBAAS;IAExBC,IAAAA,gBAAS,EAAC;QACR,MAAMlH,UAAU,CAACc;YACf,IAAI;gBACF,MAAMwB,MAAM1B,KAAKgE,KAAK,CAAC9D,MAAMqG,IAAI;gBACjC,MAAMC,qBAAqBL,2CAAAA,wBAA0BzE;gBACrD,IAAI,CAAC8E,oBAAoB;oBACvB/E,eAAeC,KAAKjC,aAAakC,QAAQvD;gBAC3C;YACF,EAAE,OAAOoB,KAAU;oBAEkCA;gBADnDqB,QAAQC,IAAI,CACV,4BAA4BZ,MAAMqG,IAAI,GAAG,OAAQ/G,CAAAA,CAAAA,aAAAA,uBAAAA,IAAKG,KAAK,YAAVH,aAAc,EAAC;YAEpE;QACF;QAEA,MAAMiH,YAAYV,aAAaW,OAAO;QACtC,IAAID,WAAW;YACbA,UAAUE,gBAAgB,CAAC,WAAWvH;QACxC;QAEA,OAAO,IAAMqH,aAAaA,UAAUG,mBAAmB,CAAC,WAAWxH;IACrE,GAAG;QAACK;QAAakC;QAAQoE;QAAc3H;QAAY+H;KAAwB;IAE3E,qBACE,6BAACU,wBAAe;QAACC,cAAcjB;QAAoBxB,OAAOA;OACvDD;AAGP"}