{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/container/RuntimeError/FrameworkIcon.tsx"], "names": ["FrameworkIcon", "framework", "svg", "data-nextjs-call-stack-framework-icon", "xmlns", "width", "height", "viewBox", "fill", "shapeRendering", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "path", "d", "mask", "id", "maskUnits", "x", "y", "circle", "cx", "cy", "r", "g", "rect", "defs", "linearGradient", "x1", "y1", "x2", "y2", "gradientUnits", "stop", "stopColor", "offset", "stopOpacity"], "mappings": ";;;;+BAGgBA;;;eAAAA;;;;gEAHE;AAGX,SAASA,cAAc,KAI7B;IAJ6B,IAAA,EAC5BC,SAAS,EAGV,GAJ6B;IAK5B,IAAIA,cAAc,SAAS;QACzB,qBACE,6BAACC;YACCC,yCAAsC;YACtCC,OAAM;YACNC,OAAM;YACNC,QAAO;YACPC,SAAQ;YACRC,MAAK;YACLC,gBAAe;YACfC,QAAO;YACPC,eAAc;YACdC,gBAAe;YACfC,aAAY;yBAEZ,6BAACC;YACCC,GAAE;YACFP,MAAK;0BAEP,6BAACM;YACCC,GAAE;YACFP,MAAK;;IAIb;IAEA,qBACE,6BAACN;QACCC,yCAAsC;QACtCC,OAAM;QACNC,OAAM;QACNC,QAAO;QACPC,SAAQ;QACRC,MAAK;qBAEL,6BAACQ;QACCC,IAAG;QACHC,WAAU;QACVC,GAAE;QACFC,GAAE;QACFf,OAAM;QACNC,QAAO;qBAEP,6BAACe;QAAOC,IAAG;QAAKC,IAAG;QAAKC,GAAE;QAAKhB,MAAK;uBAEtC,6BAACiB;QAAET,MAAK;qBACN,6BAACK;QACCC,IAAG;QACHC,IAAG;QACHC,GAAE;QACFhB,MAAK;QACLE,QAAO;QACPG,aAAY;sBAEd,6BAACC;QACCC,GAAE;QACFP,MAAK;sBAEP,6BAACkB;QACCP,GAAE;QACFC,GAAE;QACFf,OAAM;QACNC,QAAO;QACPE,MAAK;uBAGT,6BAACmB,4BACC,6BAACC;QACCX,IAAG;QACHY,IAAG;QACHC,IAAG;QACHC,IAAG;QACHC,IAAG;QACHC,eAAc;qBAEd,6BAACC;QAAKC,WAAU;sBAChB,6BAACD;QAAKE,QAAO;QAAID,WAAU;QAAQE,aAAY;uBAEjD,6BAACT;QACCX,IAAG;QACHY,IAAG;QACHC,IAAG;QACHC,IAAG;QACHC,IAAG;QACHC,eAAc;qBAEd,6BAACC;QAAKC,WAAU;sBAChB,6BAACD;QAAKE,QAAO;QAAID,WAAU;QAAQE,aAAY;;AAKzD"}