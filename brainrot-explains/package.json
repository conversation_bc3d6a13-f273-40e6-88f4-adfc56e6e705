{"name": "brainrot-explains", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"next": "^13.5.6", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "eslint": "^9", "eslint-config-next": "^13.5.6", "tailwindcss": "^4", "typescript": "^5"}}