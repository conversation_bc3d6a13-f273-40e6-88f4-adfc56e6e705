# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Text-to-Speech API Configuration (e.g., ElevenLabs, Uberduck)
TTS_API_KEY=your_tts_api_key_here
TTS_API_URL=https://api.elevenlabs.io/v1

# Voice IDs for <PERSON><PERSON><PERSON> and <PERSON>
STEWIE_VOICE_ID=your_stewie_voice_id
PETER_VOICE_ID=your_peter_voice_id

# Video Processing Configuration
FFMPEG_PATH=/usr/local/bin/ffmpeg
TEMP_DIR=./temp

# File Storage Configuration
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=50MB

# Database Configuration (if needed for user management)
DATABASE_URL=your_database_url_here

# Next.js Configuration
NEXTAUTH_SECRET=your_nextauth_secret_here
NEXTAUTH_URL=http://localhost:3000

# External APIs
YOUTUBE_API_KEY=your_youtube_api_key_here
TIKTOK_API_KEY=your_tiktok_api_key_here
